# 🚀 Project-Based Learning Hub

This repository is my personal **Project-Based Learning (PBL) hub**.  
Each branch is dedicated to learning one technology through **projects**, not just theory.  

---

## 📂 Branches Overview  

### 🔹 [C++ Branch](https://github.com/bigalex95/PBLhub/tree/cpp)  
- Learn modern C++ through progressively harder projects.  
- Covers basics, memory management, OOP, and real-world apps.  
- See details in [`cpp/README.md`](https://github.com/bigalex95/PBLhub/blob/cpp/README.md).  

### 🔹 [Python Branch](https://github.com/bigalex95/PBLhub/tree/python)  
- Python for scripting, automation, and quick prototypes.  
- Projects focused on data processing, APIs, and tools.  
- See [`python/README.md`](https://github.com/bigalex95/PBLhub/blob/python/README.md).  

---

## 📖 How to Use This Repository  

1. Clone the repo:  
   ```bash
   git clone https://github.com/bigalex95/PBLhub.git
   ```
2. Checkout a specific branch:
   ```bash
   git checkout [branch name]
   For example:
   git checkout cpp
   ```
3. Each branch has its own README.md with explanation and how to run the projects.
